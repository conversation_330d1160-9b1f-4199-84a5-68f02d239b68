/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import type * as Types from '../models.gen';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type MerchantApplicationQueryVariables = Types.Exact<{
  applicationId: Types.Scalars['Int']['input'];
}>;

export type MerchantApplicationQuery = {
  __typename?: 'Query';
  merchant_application?: {
    __typename?: 'Application';
    id: number;
    schedule_type: Types.ApplicationScheduleType;
    rejected_at?: number | null;
    signed_at?: number | null;
    is_test: boolean;
    purchase_url: string;
    from_retail: boolean;
    credit_info?: {
      __typename?: 'ApplicationCreditInfo';
      cashier_bonus_amount: number;
    } | null;
  } | null;
};

export type MerchantApplicationsQueryVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
  createdByUserId?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  page?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  orderBy?: Types.InputMaybe<Types.MerchantApplicationsOrderBy>;
  direction?: Types.InputMaybe<Types.Direction>;
  merchantReference?: Types.InputMaybe<Types.Scalars['String']['input']>;
  merchantRefOrUserName?: Types.InputMaybe<Types.Scalars['String']['input']>;
  statuses?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.AdminApplicationStatuses>>
    | Types.InputMaybe<Types.AdminApplicationStatuses>
  >;
  dateRange?: Types.InputMaybe<Types.DateRange>;
  balanceAtTimestamp?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  userName?: Types.InputMaybe<Types.Scalars['String']['input']>;
  scheduleTypes?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.ApplicationScheduleType>>
    | Types.InputMaybe<Types.ApplicationScheduleType>
  >;
}>;

export type MerchantApplicationsQuery = {
  __typename?: 'Query';
  applications?: {
    __typename?: 'merchant_application_indexPagination';
    total: number;
    has_more_pages: boolean;
    data?: Array<{
      __typename?: 'MerchantApplicationIndex';
      id: number;
      schedule_type: Types.ApplicationScheduleType;
      for_private_person: boolean;
      rejected_at?: number | null;
      signed_at?: number | null;
      status: Types.ApplicationStatus;
      created_at: number;
      processed_at?: number | null;
      requested_amount: number;
      invoice_reference_nr?: string | null;
      from_retail: boolean;
      simple_eligibility_status: string;
      user_id?: number | null;
      user_info?: {
        __typename?: 'ApplicationUserInfo';
        first_name?: string | null;
        last_name?: string | null;
      } | null;
      legal_person_info?: {
        __typename?: 'ApplicationLegalPersonInfo';
        name?: string | null;
      } | null;
      application_reference?: {
        __typename?: 'ApplicationReference';
        creator?: {
          __typename?: 'User';
          profile?: {
            __typename?: 'UserProfile';
            first_name?: string | null;
            last_name?: string | null;
          } | null;
        } | null;
      } | null;
      merchant_data?: {
        __typename?: 'ApplicationMerchantData';
        reference: string;
        store?: { __typename?: 'MerchantStore'; name: string } | null;
      } | null;
    } | null> | null;
  } | null;
};

export type ApplicationCreditInfoBasicFragment = {
  __typename?: 'ApplicationCreditInfo';
  net_total: number;
  total_expected?: number | null;
  period_months: number;
  irr?: number | null;
  monthly_payment?: number | null;
  management_fee: number;
  contract_fee: number;
  merchant_financing_pct: number;
  technology_cost_pct: number;
  bonus_pct: number;
  bonus_type?: Types.CreditInfoBonusType | null;
  cashier_bonus_amount: number;
};

export type MerchantDataBasicFragment = {
  __typename?: 'ApplicationMerchantData';
  reference: string;
  store?: { __typename?: 'MerchantStore'; name: string } | null;
};

export type ApplicationBasicFragment = {
  __typename?: 'Application';
  type: Types.ApplicationType;
  id: number;
  user_id?: number | null;
  for_private_person: boolean;
  invoice_reference_nr?: string | null;
  requested_amount: number;
  signed_at?: number | null;
  processed_at?: number | null;
  status: Types.ApplicationStatus;
  created_at: number;
  cancelled_at?: number | null;
  rejected_at?: number | null;
  bonus_paid_at?: string | null;
  from_retail: boolean;
  schedule_type: Types.ApplicationScheduleType;
  simple_eligibility_status: string;
  is_refundable: boolean;
  refunded_amount: number;
  application_reference?: {
    __typename?: 'ApplicationReference';
    short_reference: string;
    creator?: {
      __typename?: 'User';
      profile?: {
        __typename?: 'UserProfile';
        first_name?: string | null;
        last_name?: string | null;
      } | null;
    } | null;
  } | null;
  user_info?: {
    __typename?: 'ApplicationUserInfo';
    first_name?: string | null;
    last_name?: string | null;
  } | null;
  credit_info?: {
    __typename?: 'ApplicationCreditInfo';
    net_total: number;
    total_expected?: number | null;
    period_months: number;
    irr?: number | null;
    monthly_payment?: number | null;
    management_fee: number;
    contract_fee: number;
    merchant_financing_pct: number;
    technology_cost_pct: number;
    bonus_pct: number;
    bonus_type?: Types.CreditInfoBonusType | null;
    cashier_bonus_amount: number;
  } | null;
  merchant_data?: {
    __typename?: 'ApplicationMerchantData';
    reference: string;
    store?: { __typename?: 'MerchantStore'; name: string } | null;
  } | null;
};

export type MerchantApplicationDetailsQueryVariables = Types.Exact<{
  applicationId: Types.Scalars['Int']['input'];
}>;

export type MerchantApplicationDetailsQuery = {
  __typename?: 'Query';
  application?: {
    __typename?: 'Application';
    purchase_url: string;
    eligibility_state?: number | null;
    initial_paid_at?: number | null;
    merchant_id?: number | null;
    cancelled_at?: number | null;
    type: Types.ApplicationType;
    id: number;
    user_id?: number | null;
    for_private_person: boolean;
    invoice_reference_nr?: string | null;
    requested_amount: number;
    signed_at?: number | null;
    processed_at?: number | null;
    status: Types.ApplicationStatus;
    created_at: number;
    rejected_at?: number | null;
    bonus_paid_at?: string | null;
    from_retail: boolean;
    schedule_type: Types.ApplicationScheduleType;
    simple_eligibility_status: string;
    is_refundable: boolean;
    refunded_amount: number;
    merchant?: {
      __typename?: 'Merchant';
      settings?: {
        __typename?: 'MerchantSettings';
        net_total_min: number;
        net_total_max: number;
      } | null;
    } | null;
    klix_payments?: Array<{
      __typename?: 'KlixPayment';
      payer_iban?: string | null;
    } | null> | null;
    legal_person_info?: {
      __typename?: 'ApplicationLegalPersonInfo';
      name?: string | null;
      legal_person_score_id?: number | null;
      legal_person_score?: {
        __typename?: 'LegalPersonScore';
        legal_person?: {
          __typename?: 'LegalPerson';
          registry_code: string;
        } | null;
      } | null;
    } | null;
    credit_info?: {
      __typename?: 'ApplicationCreditInfo';
      credit_income: number;
      merchant_monthly_payment: number;
      merchant_credit_income: number;
      technology_cost_amount: number;
      merchant_irr: number;
      down_payment: number;
      merchant_initial_amount: number;
      merchant_financing_amount: number;
      merchant_bonus_amount: number;
      net_total: number;
      total_expected?: number | null;
      period_months: number;
      irr?: number | null;
      monthly_payment?: number | null;
      management_fee: number;
      contract_fee: number;
      merchant_financing_pct: number;
      technology_cost_pct: number;
      bonus_pct: number;
      bonus_type?: Types.CreditInfoBonusType | null;
      cashier_bonus_amount: number;
    } | null;
    first_installment?: {
      __typename?: 'Installment';
      application_id: number;
      due_at: string;
    } | null;
    last_installment?: {
      __typename?: 'Installment';
      due_at: string;
      application_id: number;
    } | null;
    installments?: Array<{
      __typename?: 'Installment';
      id?: number | null;
      due_at: string;
      amount: number;
      paid: number;
      type?: Types.InstallmentinstallmentDate | null;
    } | null> | null;
    buyback_settings?: {
      __typename?: 'ApplicationBuybackSettings';
      bought_back_at?: string | null;
      buyback_amount?: number | null;
      buyback_days: number;
      buyback_discount_pct: number;
    } | null;
    cancellation_request?: {
      __typename?: 'ApplicationCancellationRequest';
      id: number;
      application_id: number;
      reason: string;
      created_at: number;
      creator?: {
        __typename?: 'User';
        profile?: {
          __typename?: 'UserProfile';
          first_name?: string | null;
          last_name?: string | null;
        } | null;
      } | null;
      merchant_creator?: { __typename?: 'Merchant'; name: string } | null;
    } | null;
    latest_modification_request?: {
      __typename?: 'ApplicationModificationRequest';
      id: number;
      application_id: number;
      new_requested_amount: number;
      reason: string;
      handled_at?: number | null;
      created_at: number;
      creator?: {
        __typename?: 'User';
        profile?: {
          __typename?: 'UserProfile';
          first_name?: string | null;
          last_name?: string | null;
        } | null;
      } | null;
      merchant_creator?: { __typename?: 'Merchant'; name: string } | null;
    } | null;
    direct_payment_refunds?: Array<{
      __typename?: 'DirectPaymentRefund';
      id: number;
      application_id: number;
      amount: number;
      status?: string | null;
      refunded_at?: number | null;
      cancelled_at?: number | null;
      created_at?: number | null;
    } | null> | null;
    modification_requests?: Array<{
      __typename?: 'ApplicationModificationRequest';
      id: number;
      new_requested_amount: number;
      reason: string;
      handled_at?: number | null;
      creator?: {
        __typename?: 'User';
        profile?: {
          __typename?: 'UserProfile';
          first_name?: string | null;
          last_name?: string | null;
        } | null;
      } | null;
      merchant_creator?: { __typename?: 'Merchant'; name: string } | null;
    } | null> | null;
    application_reference?: {
      __typename?: 'ApplicationReference';
      short_reference: string;
      creator?: {
        __typename?: 'User';
        profile?: {
          __typename?: 'UserProfile';
          first_name?: string | null;
          last_name?: string | null;
        } | null;
      } | null;
    } | null;
    user_info?: {
      __typename?: 'ApplicationUserInfo';
      first_name?: string | null;
      last_name?: string | null;
    } | null;
    merchant_data?: {
      __typename?: 'ApplicationMerchantData';
      reference: string;
      store?: { __typename?: 'MerchantStore'; name: string } | null;
    } | null;
  } | null;
};

export type CashierApplicationSalesBonusQueryVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
}>;

export type CashierApplicationSalesBonusQuery = {
  __typename?: 'Query';
  cashier_bonus?: {
    __typename?: 'CashierApplicationSales';
    all_time_bonus_paid: number;
    all_time_bonus_unpaid: number;
    all_time_bonus_amount: number;
  } | null;
};

export type UpdateMerchantApplicationMutationVariables = Types.Exact<{
  applicationId: Types.Scalars['Int']['input'];
  reference: Types.Scalars['String']['input'];
}>;

export type UpdateMerchantApplicationMutation = {
  __typename?: 'Mutation';
  application?: {
    __typename?: 'Application';
    id: number;
    merchant_data?: {
      __typename?: 'ApplicationMerchantData';
      reference: string;
      store?: { __typename?: 'MerchantStore'; name: string } | null;
    } | null;
  } | null;
};

export type ModifyApplicationAmountMutationVariables = Types.Exact<{
  applicationId: Types.Scalars['Int']['input'];
  newRequestedAmount: Types.Scalars['Float']['input'];
  reason: Types.Scalars['String']['input'];
}>;

export type ModifyApplicationAmountMutation = {
  __typename?: 'Mutation';
  request?: {
    __typename?: 'ApplicationModificationRequest';
    id: number;
    application_id: number;
    new_requested_amount: number;
    reason: string;
    handled_at?: number | null;
  } | null;
};

export type CancelApplicationMutationVariables = Types.Exact<{
  applicationId: Types.Scalars['Int']['input'];
  reason: Types.Scalars['String']['input'];
}>;

export type CancelApplicationMutation = {
  __typename?: 'Mutation';
  request?: {
    __typename?: 'ApplicationCancellationRequest';
    id: number;
    application_id: number;
    reason: string;
  } | null;
};

export type RefundDirectPaymentMutationVariables = Types.Exact<{
  applicationId: Types.Scalars['Int']['input'];
  refundAmount: Types.Scalars['Float']['input'];
  paymentProvider: Types.Scalars['String']['input'];
}>;

export type RefundDirectPaymentMutation = {
  __typename?: 'Mutation';
  request?: {
    __typename?: 'DirectPaymentRefund';
    id: number;
    amount: number;
    status?: string | null;
    refunded_at?: number | null;
    cancelled_at?: number | null;
  } | null;
};

export const ApplicationCreditInfoBasicFragmentDoc = gql`
  fragment ApplicationCreditInfoBasic on ApplicationCreditInfo {
    net_total
    total_expected
    period_months
    irr
    monthly_payment
    management_fee
    contract_fee
    merchant_financing_pct
    technology_cost_pct
    bonus_pct
    bonus_type
    cashier_bonus_amount
  }
`;
export const MerchantDataBasicFragmentDoc = gql`
  fragment MerchantDataBasic on ApplicationMerchantData {
    reference
    store {
      name
    }
  }
`;
export const ApplicationBasicFragmentDoc = gql`
  fragment ApplicationBasic on Application {
    type
    id
    user_id
    for_private_person
    invoice_reference_nr
    requested_amount
    signed_at
    processed_at
    status
    created_at
    cancelled_at
    rejected_at
    bonus_paid_at
    from_retail
    schedule_type
    simple_eligibility_status
    is_refundable
    refunded_amount
    application_reference {
      short_reference
      creator {
        profile {
          first_name
          last_name
        }
      }
    }
    user_info {
      first_name
      last_name
    }
    credit_info {
      ...ApplicationCreditInfoBasic
    }
    merchant_data {
      ...MerchantDataBasic
    }
  }
  ${ApplicationCreditInfoBasicFragmentDoc}
  ${MerchantDataBasicFragmentDoc}
`;
export const MerchantApplicationDocument = gql`
  query merchantApplication($applicationId: Int!) {
    merchant_application(application_id: $applicationId) {
      id
      schedule_type
      rejected_at
      signed_at
      is_test
      purchase_url
      from_retail
      credit_info {
        cashier_bonus_amount
      }
    }
  }
`;

/**
 * __useMerchantApplicationQuery__
 *
 * To run a query within a React component, call `useMerchantApplicationQuery` and pass it any options that fit your needs.
 * When your component renders, `useMerchantApplicationQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMerchantApplicationQuery({
 *   variables: {
 *      applicationId: // value for 'applicationId'
 *   },
 * });
 */
export function useMerchantApplicationQuery(
  baseOptions: Apollo.QueryHookOptions<
    MerchantApplicationQuery,
    MerchantApplicationQueryVariables
  > &
    (
      | { variables: MerchantApplicationQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    MerchantApplicationQuery,
    MerchantApplicationQueryVariables
  >(MerchantApplicationDocument, options);
}
export function useMerchantApplicationLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    MerchantApplicationQuery,
    MerchantApplicationQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    MerchantApplicationQuery,
    MerchantApplicationQueryVariables
  >(MerchantApplicationDocument, options);
}
export function useMerchantApplicationSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    MerchantApplicationQuery,
    MerchantApplicationQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    MerchantApplicationQuery,
    MerchantApplicationQueryVariables
  >(MerchantApplicationDocument, options);
}
export type MerchantApplicationQueryHookResult = ReturnType<
  typeof useMerchantApplicationQuery
>;
export type MerchantApplicationLazyQueryHookResult = ReturnType<
  typeof useMerchantApplicationLazyQuery
>;
export type MerchantApplicationSuspenseQueryHookResult = ReturnType<
  typeof useMerchantApplicationSuspenseQuery
>;
export type MerchantApplicationQueryResult = Apollo.QueryResult<
  MerchantApplicationQuery,
  MerchantApplicationQueryVariables
>;
export const MerchantApplicationsDocument = gql`
  query merchantApplications(
    $merchantId: Int!
    $createdByUserId: Int
    $limit: Int
    $page: Int
    $orderBy: MerchantApplicationsOrderBy
    $direction: Direction
    $merchantReference: String
    $merchantRefOrUserName: String
    $statuses: [admin_application_statuses]
    $dateRange: DateRange
    $balanceAtTimestamp: Int
    $userName: String
    $scheduleTypes: [ApplicationScheduleType]
  ) {
    applications: merchant_applications(
      merchant_id: $merchantId
      created_by_user_id: $createdByUserId
      limit: $limit
      page: $page
      orderBy: $orderBy
      direction: $direction
      merchant_reference: $merchantReference
      merchant_ref_or_user_name: $merchantRefOrUserName
      statuses: $statuses
      date_range: $dateRange
      balance_at_timestamp: $balanceAtTimestamp
      user_name: $userName
      schedule_types: $scheduleTypes
    ) {
      data {
        id
        schedule_type
        for_private_person
        rejected_at
        signed_at
        status
        created_at
        processed_at
        requested_amount
        invoice_reference_nr
        from_retail
        simple_eligibility_status
        user_id
        schedule_type
        user_info {
          first_name
          last_name
        }
        legal_person_info {
          name
        }
        application_reference {
          creator {
            profile {
              first_name
              last_name
            }
          }
        }
        merchant_data {
          reference
          store {
            name
          }
        }
      }
      total
      has_more_pages
    }
  }
`;

/**
 * __useMerchantApplicationsQuery__
 *
 * To run a query within a React component, call `useMerchantApplicationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useMerchantApplicationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMerchantApplicationsQuery({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *      createdByUserId: // value for 'createdByUserId'
 *      limit: // value for 'limit'
 *      page: // value for 'page'
 *      orderBy: // value for 'orderBy'
 *      direction: // value for 'direction'
 *      merchantReference: // value for 'merchantReference'
 *      merchantRefOrUserName: // value for 'merchantRefOrUserName'
 *      statuses: // value for 'statuses'
 *      dateRange: // value for 'dateRange'
 *      balanceAtTimestamp: // value for 'balanceAtTimestamp'
 *      userName: // value for 'userName'
 *      scheduleTypes: // value for 'scheduleTypes'
 *   },
 * });
 */
export function useMerchantApplicationsQuery(
  baseOptions: Apollo.QueryHookOptions<
    MerchantApplicationsQuery,
    MerchantApplicationsQueryVariables
  > &
    (
      | { variables: MerchantApplicationsQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    MerchantApplicationsQuery,
    MerchantApplicationsQueryVariables
  >(MerchantApplicationsDocument, options);
}
export function useMerchantApplicationsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    MerchantApplicationsQuery,
    MerchantApplicationsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    MerchantApplicationsQuery,
    MerchantApplicationsQueryVariables
  >(MerchantApplicationsDocument, options);
}
export function useMerchantApplicationsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    MerchantApplicationsQuery,
    MerchantApplicationsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    MerchantApplicationsQuery,
    MerchantApplicationsQueryVariables
  >(MerchantApplicationsDocument, options);
}
export type MerchantApplicationsQueryHookResult = ReturnType<
  typeof useMerchantApplicationsQuery
>;
export type MerchantApplicationsLazyQueryHookResult = ReturnType<
  typeof useMerchantApplicationsLazyQuery
>;
export type MerchantApplicationsSuspenseQueryHookResult = ReturnType<
  typeof useMerchantApplicationsSuspenseQuery
>;
export type MerchantApplicationsQueryResult = Apollo.QueryResult<
  MerchantApplicationsQuery,
  MerchantApplicationsQueryVariables
>;
export const MerchantApplicationDetailsDocument = gql`
  query merchantApplicationDetails($applicationId: Int!) {
    application: merchant_application(application_id: $applicationId) {
      ...ApplicationBasic
      purchase_url
      eligibility_state
      initial_paid_at
      merchant_id
      merchant {
        settings {
          net_total_min
          net_total_max
        }
      }
      klix_payments {
        payer_iban
      }
      legal_person_info {
        name
        legal_person_score_id
        legal_person_score {
          legal_person {
            registry_code
          }
        }
      }
      credit_info {
        ...ApplicationCreditInfoBasic
        credit_income
        merchant_monthly_payment
        merchant_credit_income
        technology_cost_amount
        merchant_irr
        down_payment
        merchant_initial_amount
        merchant_financing_amount
        merchant_bonus_amount
      }
      first_installment {
        application_id
        due_at
      }
      last_installment {
        due_at
        application_id
      }
      installments {
        id
        due_at
        amount
        paid
        type
      }
      buyback_settings {
        bought_back_at
        buyback_amount
        buyback_days
        buyback_discount_pct
      }
      cancelled_at
      cancellation_request {
        id
        application_id
        reason
        created_at
        creator {
          profile {
            first_name
            last_name
          }
        }
        merchant_creator {
          name
        }
      }
      latest_modification_request {
        id
        application_id
        new_requested_amount
        reason
        handled_at
        created_at
        creator {
          profile {
            first_name
            last_name
          }
        }
        merchant_creator {
          name
        }
      }
      direct_payment_refunds {
        id
        application_id
        amount
        status
        refunded_at
        cancelled_at
        created_at
      }
      modification_requests {
        id
        new_requested_amount
        creator {
          profile {
            first_name
            last_name
          }
        }
        merchant_creator {
          name
        }
        reason
        handled_at
      }
    }
  }
  ${ApplicationBasicFragmentDoc}
  ${ApplicationCreditInfoBasicFragmentDoc}
`;

/**
 * __useMerchantApplicationDetailsQuery__
 *
 * To run a query within a React component, call `useMerchantApplicationDetailsQuery` and pass it any options that fit your needs.
 * When your component renders, `useMerchantApplicationDetailsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMerchantApplicationDetailsQuery({
 *   variables: {
 *      applicationId: // value for 'applicationId'
 *   },
 * });
 */
export function useMerchantApplicationDetailsQuery(
  baseOptions: Apollo.QueryHookOptions<
    MerchantApplicationDetailsQuery,
    MerchantApplicationDetailsQueryVariables
  > &
    (
      | { variables: MerchantApplicationDetailsQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    MerchantApplicationDetailsQuery,
    MerchantApplicationDetailsQueryVariables
  >(MerchantApplicationDetailsDocument, options);
}
export function useMerchantApplicationDetailsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    MerchantApplicationDetailsQuery,
    MerchantApplicationDetailsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    MerchantApplicationDetailsQuery,
    MerchantApplicationDetailsQueryVariables
  >(MerchantApplicationDetailsDocument, options);
}
export function useMerchantApplicationDetailsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    MerchantApplicationDetailsQuery,
    MerchantApplicationDetailsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    MerchantApplicationDetailsQuery,
    MerchantApplicationDetailsQueryVariables
  >(MerchantApplicationDetailsDocument, options);
}
export type MerchantApplicationDetailsQueryHookResult = ReturnType<
  typeof useMerchantApplicationDetailsQuery
>;
export type MerchantApplicationDetailsLazyQueryHookResult = ReturnType<
  typeof useMerchantApplicationDetailsLazyQuery
>;
export type MerchantApplicationDetailsSuspenseQueryHookResult = ReturnType<
  typeof useMerchantApplicationDetailsSuspenseQuery
>;
export type MerchantApplicationDetailsQueryResult = Apollo.QueryResult<
  MerchantApplicationDetailsQuery,
  MerchantApplicationDetailsQueryVariables
>;
export const CashierApplicationSalesBonusDocument = gql`
  query CashierApplicationSalesBonus($merchantId: Int!) {
    cashier_bonus: cashier_application_sales(merchant_id: $merchantId) {
      all_time_bonus_paid
      all_time_bonus_unpaid
      all_time_bonus_amount
    }
  }
`;

/**
 * __useCashierApplicationSalesBonusQuery__
 *
 * To run a query within a React component, call `useCashierApplicationSalesBonusQuery` and pass it any options that fit your needs.
 * When your component renders, `useCashierApplicationSalesBonusQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCashierApplicationSalesBonusQuery({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *   },
 * });
 */
export function useCashierApplicationSalesBonusQuery(
  baseOptions: Apollo.QueryHookOptions<
    CashierApplicationSalesBonusQuery,
    CashierApplicationSalesBonusQueryVariables
  > &
    (
      | {
          variables: CashierApplicationSalesBonusQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    CashierApplicationSalesBonusQuery,
    CashierApplicationSalesBonusQueryVariables
  >(CashierApplicationSalesBonusDocument, options);
}
export function useCashierApplicationSalesBonusLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    CashierApplicationSalesBonusQuery,
    CashierApplicationSalesBonusQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    CashierApplicationSalesBonusQuery,
    CashierApplicationSalesBonusQueryVariables
  >(CashierApplicationSalesBonusDocument, options);
}
export function useCashierApplicationSalesBonusSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    CashierApplicationSalesBonusQuery,
    CashierApplicationSalesBonusQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    CashierApplicationSalesBonusQuery,
    CashierApplicationSalesBonusQueryVariables
  >(CashierApplicationSalesBonusDocument, options);
}
export type CashierApplicationSalesBonusQueryHookResult = ReturnType<
  typeof useCashierApplicationSalesBonusQuery
>;
export type CashierApplicationSalesBonusLazyQueryHookResult = ReturnType<
  typeof useCashierApplicationSalesBonusLazyQuery
>;
export type CashierApplicationSalesBonusSuspenseQueryHookResult = ReturnType<
  typeof useCashierApplicationSalesBonusSuspenseQuery
>;
export type CashierApplicationSalesBonusQueryResult = Apollo.QueryResult<
  CashierApplicationSalesBonusQuery,
  CashierApplicationSalesBonusQueryVariables
>;
export const UpdateMerchantApplicationDocument = gql`
  mutation UpdateMerchantApplication(
    $applicationId: Int!
    $reference: String!
  ) {
    application: merchant_update_application(
      application_id: $applicationId
      reference: $reference
    ) {
      id
      merchant_data {
        ...MerchantDataBasic
      }
    }
  }
  ${MerchantDataBasicFragmentDoc}
`;
export type UpdateMerchantApplicationMutationFn = Apollo.MutationFunction<
  UpdateMerchantApplicationMutation,
  UpdateMerchantApplicationMutationVariables
>;

/**
 * __useUpdateMerchantApplicationMutation__
 *
 * To run a mutation, you first call `useUpdateMerchantApplicationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateMerchantApplicationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateMerchantApplicationMutation, { data, loading, error }] = useUpdateMerchantApplicationMutation({
 *   variables: {
 *      applicationId: // value for 'applicationId'
 *      reference: // value for 'reference'
 *   },
 * });
 */
export function useUpdateMerchantApplicationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateMerchantApplicationMutation,
    UpdateMerchantApplicationMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateMerchantApplicationMutation,
    UpdateMerchantApplicationMutationVariables
  >(UpdateMerchantApplicationDocument, options);
}
export type UpdateMerchantApplicationMutationHookResult = ReturnType<
  typeof useUpdateMerchantApplicationMutation
>;
export type UpdateMerchantApplicationMutationResult =
  Apollo.MutationResult<UpdateMerchantApplicationMutation>;
export type UpdateMerchantApplicationMutationOptions =
  Apollo.BaseMutationOptions<
    UpdateMerchantApplicationMutation,
    UpdateMerchantApplicationMutationVariables
  >;
export const ModifyApplicationAmountDocument = gql`
  mutation ModifyApplicationAmount(
    $applicationId: Int!
    $newRequestedAmount: Float!
    $reason: String!
  ) {
    request: store_application_modification_request(
      application_id: $applicationId
      new_requested_amount: $newRequestedAmount
      reason: $reason
    ) {
      id
      application_id
      new_requested_amount
      reason
      handled_at
    }
  }
`;
export type ModifyApplicationAmountMutationFn = Apollo.MutationFunction<
  ModifyApplicationAmountMutation,
  ModifyApplicationAmountMutationVariables
>;

/**
 * __useModifyApplicationAmountMutation__
 *
 * To run a mutation, you first call `useModifyApplicationAmountMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useModifyApplicationAmountMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [modifyApplicationAmountMutation, { data, loading, error }] = useModifyApplicationAmountMutation({
 *   variables: {
 *      applicationId: // value for 'applicationId'
 *      newRequestedAmount: // value for 'newRequestedAmount'
 *      reason: // value for 'reason'
 *   },
 * });
 */
export function useModifyApplicationAmountMutation(
  baseOptions?: Apollo.MutationHookOptions<
    ModifyApplicationAmountMutation,
    ModifyApplicationAmountMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    ModifyApplicationAmountMutation,
    ModifyApplicationAmountMutationVariables
  >(ModifyApplicationAmountDocument, options);
}
export type ModifyApplicationAmountMutationHookResult = ReturnType<
  typeof useModifyApplicationAmountMutation
>;
export type ModifyApplicationAmountMutationResult =
  Apollo.MutationResult<ModifyApplicationAmountMutation>;
export type ModifyApplicationAmountMutationOptions = Apollo.BaseMutationOptions<
  ModifyApplicationAmountMutation,
  ModifyApplicationAmountMutationVariables
>;
export const CancelApplicationDocument = gql`
  mutation CancelApplication($applicationId: Int!, $reason: String!) {
    request: store_application_cancellation_request(
      application_id: $applicationId
      reason: $reason
    ) {
      id
      application_id
      reason
    }
  }
`;
export type CancelApplicationMutationFn = Apollo.MutationFunction<
  CancelApplicationMutation,
  CancelApplicationMutationVariables
>;

/**
 * __useCancelApplicationMutation__
 *
 * To run a mutation, you first call `useCancelApplicationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCancelApplicationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [cancelApplicationMutation, { data, loading, error }] = useCancelApplicationMutation({
 *   variables: {
 *      applicationId: // value for 'applicationId'
 *      reason: // value for 'reason'
 *   },
 * });
 */
export function useCancelApplicationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CancelApplicationMutation,
    CancelApplicationMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CancelApplicationMutation,
    CancelApplicationMutationVariables
  >(CancelApplicationDocument, options);
}
export type CancelApplicationMutationHookResult = ReturnType<
  typeof useCancelApplicationMutation
>;
export type CancelApplicationMutationResult =
  Apollo.MutationResult<CancelApplicationMutation>;
export type CancelApplicationMutationOptions = Apollo.BaseMutationOptions<
  CancelApplicationMutation,
  CancelApplicationMutationVariables
>;
export const RefundDirectPaymentDocument = gql`
  mutation RefundDirectPayment(
    $applicationId: Int!
    $refundAmount: Float!
    $paymentProvider: String!
  ) {
    request: create_direct_payment_refund_request(
      application_id: $applicationId
      amount: $refundAmount
      payment_provider: $paymentProvider
    ) {
      id
      amount
      status
      refunded_at
      cancelled_at
    }
  }
`;
export type RefundDirectPaymentMutationFn = Apollo.MutationFunction<
  RefundDirectPaymentMutation,
  RefundDirectPaymentMutationVariables
>;

/**
 * __useRefundDirectPaymentMutation__
 *
 * To run a mutation, you first call `useRefundDirectPaymentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRefundDirectPaymentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [refundDirectPaymentMutation, { data, loading, error }] = useRefundDirectPaymentMutation({
 *   variables: {
 *      applicationId: // value for 'applicationId'
 *      refundAmount: // value for 'refundAmount'
 *      paymentProvider: // value for 'paymentProvider'
 *   },
 * });
 */
export function useRefundDirectPaymentMutation(
  baseOptions?: Apollo.MutationHookOptions<
    RefundDirectPaymentMutation,
    RefundDirectPaymentMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    RefundDirectPaymentMutation,
    RefundDirectPaymentMutationVariables
  >(RefundDirectPaymentDocument, options);
}
export type RefundDirectPaymentMutationHookResult = ReturnType<
  typeof useRefundDirectPaymentMutation
>;
export type RefundDirectPaymentMutationResult =
  Apollo.MutationResult<RefundDirectPaymentMutation>;
export type RefundDirectPaymentMutationOptions = Apollo.BaseMutationOptions<
  RefundDirectPaymentMutation,
  RefundDirectPaymentMutationVariables
>;
