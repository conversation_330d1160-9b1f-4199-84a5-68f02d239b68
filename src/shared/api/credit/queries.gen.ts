/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import type * as Types from '../models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type CreditSettingsQueryVariables = Types.Exact<{
  netTotal?: Types.InputMaybe<Types.Scalars['Float']['input']>;
  merchantId?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  scheduleType?: Types.InputMaybe<Types.ApplicationScheduleType>;
}>;

export type CreditSettingsQuery = {
  __typename?: 'Query';
  credit_settings?: Array<{
    __typename?: 'CreditSetting';
    net_total: number;
    month: number;
    contract_fee: number;
    annual_pct_rate: number;
    management_fee: number;
    irr: number;
    monthly_payment: number;
  } | null> | null;
};

export const CreditSettingsDocument = gql`
  query CreditSettings(
    $netTotal: Float
    $merchantId: Int
    $scheduleType: ApplicationScheduleType
  ) {
    credit_settings(
      net_total: $netTotal
      merchant_id: $merchantId
      schedule_type: $scheduleType
    ) {
      net_total
      month
      contract_fee
      annual_pct_rate
      management_fee
      irr
      monthly_payment
    }
  }
`;

/**
 * __useCreditSettingsQuery__
 *
 * To run a query within a React component, call `useCreditSettingsQuery` and pass it any options that fit your needs.
 * When your component renders, `useCreditSettingsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCreditSettingsQuery({
 *   variables: {
 *      netTotal: // value for 'netTotal'
 *      merchantId: // value for 'merchantId'
 *      scheduleType: // value for 'scheduleType'
 *   },
 * });
 */
export function useCreditSettingsQuery(
  baseOptions?: Apollo.QueryHookOptions<
    CreditSettingsQuery,
    CreditSettingsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<CreditSettingsQuery, CreditSettingsQueryVariables>(
    CreditSettingsDocument,
    options,
  );
}
export function useCreditSettingsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    CreditSettingsQuery,
    CreditSettingsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<CreditSettingsQuery, CreditSettingsQueryVariables>(
    CreditSettingsDocument,
    options,
  );
}
export function useCreditSettingsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    CreditSettingsQuery,
    CreditSettingsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    CreditSettingsQuery,
    CreditSettingsQueryVariables
  >(CreditSettingsDocument, options);
}
export type CreditSettingsQueryHookResult = ReturnType<
  typeof useCreditSettingsQuery
>;
export type CreditSettingsLazyQueryHookResult = ReturnType<
  typeof useCreditSettingsLazyQuery
>;
export type CreditSettingsSuspenseQueryHookResult = ReturnType<
  typeof useCreditSettingsSuspenseQuery
>;
export type CreditSettingsQueryResult = Apollo.QueryResult<
  CreditSettingsQuery,
  CreditSettingsQueryVariables
>;
