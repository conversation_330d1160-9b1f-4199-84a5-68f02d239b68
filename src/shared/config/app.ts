import {
  AppAuthMethod,
  type AppConfig,
  type AppConfigByCountry,
  AppLanguage,
  ConsumerLoanProduct,
  CountryPhoneCode,
  NonLoanProduct,
  SupportedCountries,
} from '../types';
import { APP_COUNTRY } from './envs';

const APP_CONFIG_BY_COUNTRY_MAP = {
  [SupportedCountries.EE]: {
    authMethods: [
      AppAuthMethod.SMART_ID,
      AppAuthMethod.MOBILE,
      AppAuthMethod.ID_CARD,
      AppAuthMethod.PASSWORD,
    ],
    termsUrlByLanguage: {
      [AppLanguage.EN]:
        'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=en',
      [AppLanguage.ET]:
        'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=et',
      [AppLanguage.RU]:
        'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=ru',
      [AppLanguage.LV]:
        'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=en',
      [AppLanguage.LT]:
        'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=en',
    },
    premiumTermsUrlByLanguage: {
      [AppLanguage.EN]:
        'https://esto-public.s3.eu-west-2.amazonaws.com/docs/en-us/ESTO Premium terms.pdf',
      [AppLanguage.ET]:
        'https://esto-public.s3.eu-west-2.amazonaws.com/docs/et-ee/ESTO Premium Tingimused.pdf',
      [AppLanguage.RU]:
        'https://esto-public.s3.eu-west-2.amazonaws.com/docs/ru-ru/Условия ESTO Premium.pdf',
      [AppLanguage.LV]:
        'https://esto-public.s3.eu-west-2.amazonaws.com/docs/en-us/ESTO Premium terms.pdf',
      [AppLanguage.LT]:
        'https://esto-public.s3.eu-west-2.amazonaws.com/docs/en-us/ESTO Premium terms.pdf',
    },
    supportUrlByLanguage: {
      [AppLanguage.EN]: 'https://estoee.zendesk.com/hc/et',
      [AppLanguage.ET]: 'https://estoee.zendesk.com/hc/et',
      [AppLanguage.RU]: 'https://estoee.zendesk.com/hc/ru',
      [AppLanguage.LV]: 'https://estoee.zendesk.com/hc/et',
      [AppLanguage.LT]: 'https://estoee.zendesk.com/hc/et',
    },
    homepageDeals: {
      [AppLanguage.EN]: 'https://esto.eu/ee/deals?lang=en',
      [AppLanguage.ET]: 'https://esto.eu/ee/deals?lang=et',
      [AppLanguage.RU]: 'https://esto.eu/ee/deals?lang=ru',
      [AppLanguage.LV]: 'https://esto.eu/ee/deals?lang=lv',
      [AppLanguage.LT]: 'https://esto.eu/ee/deals?lang=lt',
    },
    seesamClaimsUrlByLanguage: {
      [AppLanguage.EN]: 'https://seesam.ee/en/claims',
      [AppLanguage.ET]: 'https://seesam.ee/et/kahjukasitlus',
      [AppLanguage.RU]: 'https://seesam.ee/ru/ushcherb',
      [AppLanguage.LV]: 'https://seesam.ee/en/claims',
      [AppLanguage.LT]: 'https://seesam.ee/en/claims',
    },
    seesamDataPolicyUrlByLanguage: {
      [AppLanguage.EN]:
        'https://seesam.ee/en/information/processing-of-personal-data',
      [AppLanguage.ET]: 'https://seesam.ee/et/teave/isikuandmete-tootlemine',
      [AppLanguage.RU]:
        'https://seesam.ee/ru/informaciya/obrabotka-personalnyh-dannyh',
      [AppLanguage.LV]:
        'https://seesam.ee/en/information/processing-of-personal-data',
      [AppLanguage.LT]:
        'https://seesam.ee/en/information/processing-of-personal-data',
    },
    starProduct: NonLoanProduct.CREDIT_LINE,
    legalInfo: {
      infoEmail: '<EMAIL>',
      phone: {
        number: '(+372) 622 52 52',
        label: '+**********',
      },
      name: 'ESTO AS',
      address: 'Harju maakond, Tallinn, Laeva tn 2, 10111',
    },
    phoneCode: CountryPhoneCode.EE,
    isRejectedCAWRedirectionToCamEnabled: true,
    isStandingPaymentEnabled: false,
    creditLine: {
      isCreditAccountInterestFreeBannerEnabled: true,
      creditLineInterestFreeDays: 30,
    },
    purchaseFlowUrl: 'https://user.v2.esto.ee',
    isOnlyPasswordGracePeriodSigningEnabled: false,
    isOnlyPasswordCreditAccountModificationSigningEnabled: false,
    isOnlyPasswordCreditAccountLimitIncreaseSigningEnabled: false,
  },
  [SupportedCountries.LV]: {
    authMethods: [
      AppAuthMethod.SMART_ID,
      AppAuthMethod.PAYSERA_BANKLINK,
      AppAuthMethod.PASSWORD,
      AppAuthMethod.EPARAKSTS_MOBILE,
      AppAuthMethod.EPARAKSTS_SMARTCARD,
    ],
    homepageDeals: {
      [AppLanguage.EN]: 'https://esto.eu/lv/deals?lang=en',
      [AppLanguage.ET]: 'https://esto.eu/lv/deals?lang=et',
      [AppLanguage.RU]: 'https://esto.eu/lv/deals?lang=ru',
      [AppLanguage.LV]: 'https://esto.eu/lv/deals?lang=lv',
      [AppLanguage.LT]: 'https://esto.eu/lv/deals?lang=lt',
    },
    termsUrlByLanguage: {
      [AppLanguage.EN]: 'https://esto.eu/lv/noteikumi-un-nosacijumi?lang=en',
      [AppLanguage.ET]: 'https://esto.eu/lv/noteikumi-un-nosacijumi?lang=en',
      [AppLanguage.RU]: 'https://esto.eu/lv/noteikumi-un-nosacijumi?lang=ru',
      [AppLanguage.LV]: 'https://esto.eu/lv/noteikumi-un-nosacijumi?lang=lv',
      [AppLanguage.LT]: 'https://esto.eu/lv/noteikumi-un-nosacijumi?lang=en',
    },
    premiumTermsUrlByLanguage: {
      [AppLanguage.EN]:
        'https://esto-lv-public.s3.eu-central-1.amazonaws.com/docs/en-us/ESTO Premium terms.pdf',
      [AppLanguage.ET]:
        'https://esto-lv-public.s3.eu-central-1.amazonaws.com/docs/en-us/ESTO Premium terms.pdf',
      [AppLanguage.RU]:
        'https://esto-lv-public.s3.eu-central-1.amazonaws.com/docs/ru-ru/ESTO Premium условия.pdf',
      [AppLanguage.LV]:
        'https://esto-lv-public.s3.eu-central-1.amazonaws.com/docs/lv-lv/ESTO Premium Noteikumi.pdf',
      [AppLanguage.LT]:
        'https://esto-lv-public.s3.eu-central-1.amazonaws.com/docs/en-us/ESTO Premium terms.pdf',
    },
    supportUrlByLanguage: {
      [AppLanguage.EN]: 'https://estolv.zendesk.com/hc/lv',
      [AppLanguage.LV]: 'https://estolv.zendesk.com/hc/lv',
      [AppLanguage.RU]: 'https://estolv.zendesk.com/hc/lv',
      [AppLanguage.LT]: 'https://estolv.zendesk.com/hc/lv',
      [AppLanguage.ET]: 'https://estolv.zendesk.com/hc/lv',
    },
    seesamClaimsUrlByLanguage: {
      [AppLanguage.EN]: 'https://seesam.ee/en/claims',
      [AppLanguage.ET]: 'https://seesam.ee/et/kahjukasitlus',
      [AppLanguage.RU]: 'https://seesam.ee/ru/ushcherb',
      [AppLanguage.LV]: 'https://seesam.ee/en/claims',
      [AppLanguage.LT]: 'https://seesam.ee/en/claims',
    },
    seesamDataPolicyUrlByLanguage: {
      [AppLanguage.EN]:
        'https://seesam.ee/en/information/processing-of-personal-data',
      [AppLanguage.ET]: 'https://seesam.ee/et/teave/isikuandmete-tootlemine',
      [AppLanguage.RU]:
        'https://seesam.ee/ru/informaciya/obrabotka-personalnyh-dannyh',
      [AppLanguage.LV]:
        'https://seesam.ee/en/information/processing-of-personal-data',
      [AppLanguage.LT]:
        'https://seesam.ee/en/information/processing-of-personal-data',
    },
    starProduct: NonLoanProduct.CREDIT_LINE,
    legalInfo: {
      infoEmail: '<EMAIL>',
      phone: {
        number: '(+371) 66 222 250',
        label: '+***********',
      },
      name: 'ESTO LV AS',
      address: 'Gustava Zemgala gatve 74, Rīga, LV-1039',
    },
    phoneCode: CountryPhoneCode.LV,
    isRejectedCAWRedirectionToCamEnabled: false,
    isStandingPaymentEnabled: false,
    creditLine: {
      isCreditAccountInterestFreeBannerEnabled: true,
      creditLineInterestFreeDays: 30,
    },
    purchaseFlowUrl: 'https://user.v2.esto.lv',
    isOnlyPasswordGracePeriodSigningEnabled: true,
    isOnlyPasswordCreditAccountModificationSigningEnabled: true,
    isOnlyPasswordCreditAccountLimitIncreaseSigningEnabled: true,
  },
  [SupportedCountries.LT]: {
    authMethods: [
      AppAuthMethod.SMART_ID,
      AppAuthMethod.MOBILE,
      AppAuthMethod.PAYSERA_BANKLINK,
      AppAuthMethod.PASSWORD,
    ],
    homepageDeals: {
      [AppLanguage.EN]: 'https://esto.eu/lt/deals?lang=en',
      [AppLanguage.ET]: 'https://esto.eu/lt/deals?lang=et',
      [AppLanguage.RU]: 'https://esto.eu/lt/deals?lang=ru',
      [AppLanguage.LV]: 'https://esto.eu/lt/deals?lang=lv',
      [AppLanguage.LT]: 'https://esto.eu/lt/deals?lang=lt',
    },
    termsUrlByLanguage: {
      [AppLanguage.EN]: 'https://esto.eu/lt/taisykles-ir-salygos?lang=en',
      [AppLanguage.ET]: 'https://esto.eu/lt/taisykles-ir-salygos?lang=en',
      [AppLanguage.RU]: 'https://esto.eu/lt/taisykles-ir-salygos?lang=ru',
      [AppLanguage.LV]: 'https://esto.eu/lt/taisykles-ir-salygos?lang=en',
      [AppLanguage.LT]: 'https://esto.eu/lt/taisykles-ir-salygos?lang=lt',
    },
    premiumTermsUrlByLanguage: {
      [AppLanguage.EN]:
        'https://esto-public.s3.eu-west-2.amazonaws.com/docs/en-us/ESTO Premium terms.pdf',
      [AppLanguage.ET]:
        'https://esto-public.s3.eu-west-2.amazonaws.com/docs/en-us/ESTO Premium terms.pdf',
      [AppLanguage.RU]:
        'https://esto-public.s3.eu-west-2.amazonaws.com/docs/ru-ru/Условия ESTO Premium.pdf',
      [AppLanguage.LV]:
        'https://esto-public.s3.eu-west-2.amazonaws.com/docs/en-us/ESTO Premium terms.pdf',
      [AppLanguage.LT]:
        'https://esto-lt-public.s3.eu-central-1.amazonaws.com/docs/lt-lt/ESTO+Premium+paslaugos+teikimo+tvarka.pdf',
    },
    supportUrlByLanguage: {
      [AppLanguage.EN]: 'https://estolt.zendesk.com/hc/lt',
      [AppLanguage.LT]: 'https://estolt.zendesk.com/hc/lt',
      [AppLanguage.RU]: 'https://estolt.zendesk.com/hc/lt',
      [AppLanguage.LV]: 'https://estolt.zendesk.com/hc/lt',
      [AppLanguage.ET]: 'https://estolt.zendesk.com/hc/lt',
    },
    seesamClaimsUrlByLanguage: {
      [AppLanguage.EN]: 'https://seesam.ee/en/claims',
      [AppLanguage.ET]: 'https://seesam.ee/et/kahjukasitlus',
      [AppLanguage.RU]: 'https://seesam.ee/ru/ushcherb',
      [AppLanguage.LV]: 'https://seesam.ee/en/claims',
      [AppLanguage.LT]: 'https://seesam.ee/en/claims',
    },
    seesamDataPolicyUrlByLanguage: {
      [AppLanguage.EN]:
        'https://seesam.ee/en/information/processing-of-personal-data',
      [AppLanguage.ET]: 'https://seesam.ee/et/teave/isikuandmete-tootlemine',
      [AppLanguage.RU]:
        'https://seesam.ee/ru/informaciya/obrabotka-personalnyh-dannyh',
      [AppLanguage.LV]:
        'https://seesam.ee/en/information/processing-of-personal-data',
      [AppLanguage.LT]:
        'https://seesam.ee/en/information/processing-of-personal-data',
    },
    starProduct: ConsumerLoanProduct.SMALL_LOAN,
    legalInfo: {
      infoEmail: '<EMAIL>',
      phone: {
        number: '(+370) 669 10 701',
        label: '+***********',
      },
      name: 'ESTO UAB',
      address: 'Lvivo g. 25, LT-09320 Vilnius, Lietuva',
    },
    phoneCode: CountryPhoneCode.LT,
    isRejectedCAWRedirectionToCamEnabled: true,
    isStandingPaymentEnabled: true,
    creditLine: {
      isCreditAccountInterestFreeBannerEnabled: true,
      creditLineInterestFreeDays: 30,
    },
    purchaseFlowUrl: 'https://user.v2.estopay.lt',
    isOnlyPasswordGracePeriodSigningEnabled: false,
    isOnlyPasswordCreditAccountModificationSigningEnabled: false,
    isOnlyPasswordCreditAccountLimitIncreaseSigningEnabled: false,
  },
} as const satisfies AppConfigByCountry;

const appConfigByCountry =
  APP_CONFIG_BY_COUNTRY_MAP[APP_COUNTRY] ?? APP_CONFIG_BY_COUNTRY_MAP.EE;

export const APP_CONFIG = {
  name: 'ESTO',
  ...appConfigByCountry,
} as const satisfies AppConfig;
