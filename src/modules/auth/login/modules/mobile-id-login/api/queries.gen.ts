/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import type * as Types from '../../../../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type MobileIdLoginPollQueryVariables = Types.Exact<{
  sessionId: Types.Scalars['String']['input'];
}>;

export type MobileIdLoginPollQuery = {
  __typename?: 'Query';
  challenge?: {
    __typename?: 'LoginChallenge';
    challenge_id?: string | null;
    session_id?: string | null;
    is_authenticated?: boolean | null;
  } | null;
};

export type MobileIdLoginChallengeMutationVariables = Types.Exact<{
  phone: Types.Scalars['String']['input'];
  pin: Types.Scalars['String']['input'];
}>;

export type MobileIdLoginChallengeMutation = {
  __typename?: 'Mutation';
  challenge?: {
    __typename?: 'LoginChallenge';
    challenge_id?: string | null;
    session_id?: string | null;
    is_authenticated?: boolean | null;
  } | null;
};

export const MobileIdLoginPollDocument = gql`
  query MobileIdLoginPoll($sessionId: String!) {
    challenge: mobile_login(session_id: $sessionId) {
      challenge_id
      session_id
      is_authenticated
    }
  }
`;

/**
 * __useMobileIdLoginPollQuery__
 *
 * To run a query within a React component, call `useMobileIdLoginPollQuery` and pass it any options that fit your needs.
 * When your component renders, `useMobileIdLoginPollQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMobileIdLoginPollQuery({
 *   variables: {
 *      sessionId: // value for 'sessionId'
 *   },
 * });
 */
export function useMobileIdLoginPollQuery(
  baseOptions: Apollo.QueryHookOptions<
    MobileIdLoginPollQuery,
    MobileIdLoginPollQueryVariables
  > &
    (
      | { variables: MobileIdLoginPollQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    MobileIdLoginPollQuery,
    MobileIdLoginPollQueryVariables
  >(MobileIdLoginPollDocument, options);
}
export function useMobileIdLoginPollLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    MobileIdLoginPollQuery,
    MobileIdLoginPollQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    MobileIdLoginPollQuery,
    MobileIdLoginPollQueryVariables
  >(MobileIdLoginPollDocument, options);
}
export function useMobileIdLoginPollSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    MobileIdLoginPollQuery,
    MobileIdLoginPollQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    MobileIdLoginPollQuery,
    MobileIdLoginPollQueryVariables
  >(MobileIdLoginPollDocument, options);
}
export type MobileIdLoginPollQueryHookResult = ReturnType<
  typeof useMobileIdLoginPollQuery
>;
export type MobileIdLoginPollLazyQueryHookResult = ReturnType<
  typeof useMobileIdLoginPollLazyQuery
>;
export type MobileIdLoginPollSuspenseQueryHookResult = ReturnType<
  typeof useMobileIdLoginPollSuspenseQuery
>;
export type MobileIdLoginPollQueryResult = Apollo.QueryResult<
  MobileIdLoginPollQuery,
  MobileIdLoginPollQueryVariables
>;
export const MobileIdLoginChallengeDocument = gql`
  mutation MobileIdLoginChallenge($phone: String!, $pin: String!) {
    challenge: mobile_login_challenge(phone: $phone, pin: $pin) {
      challenge_id
      session_id
      is_authenticated
    }
  }
`;
export type MobileIdLoginChallengeMutationFn = Apollo.MutationFunction<
  MobileIdLoginChallengeMutation,
  MobileIdLoginChallengeMutationVariables
>;

/**
 * __useMobileIdLoginChallengeMutation__
 *
 * To run a mutation, you first call `useMobileIdLoginChallengeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useMobileIdLoginChallengeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [mobileIdLoginChallengeMutation, { data, loading, error }] = useMobileIdLoginChallengeMutation({
 *   variables: {
 *      phone: // value for 'phone'
 *      pin: // value for 'pin'
 *   },
 * });
 */
export function useMobileIdLoginChallengeMutation(
  baseOptions?: Apollo.MutationHookOptions<
    MobileIdLoginChallengeMutation,
    MobileIdLoginChallengeMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    MobileIdLoginChallengeMutation,
    MobileIdLoginChallengeMutationVariables
  >(MobileIdLoginChallengeDocument, options);
}
export type MobileIdLoginChallengeMutationHookResult = ReturnType<
  typeof useMobileIdLoginChallengeMutation
>;
export type MobileIdLoginChallengeMutationResult =
  Apollo.MutationResult<MobileIdLoginChallengeMutation>;
export type MobileIdLoginChallengeMutationOptions = Apollo.BaseMutationOptions<
  MobileIdLoginChallengeMutation,
  MobileIdLoginChallengeMutationVariables
>;
