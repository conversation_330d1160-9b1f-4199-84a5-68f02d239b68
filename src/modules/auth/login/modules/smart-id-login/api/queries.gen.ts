/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import type * as Types from '../../../../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type SmartIdLoginChallengeMutationVariables = Types.Exact<{
  pin: Types.Scalars['String']['input'];
}>;

export type SmartIdLoginChallengeMutation = {
  __typename?: 'Mutation';
  challenge?: {
    __typename?: 'LoginChallenge';
    challenge_id?: string | null;
    session_id?: string | null;
    is_authenticated?: boolean | null;
  } | null;
};

export type SmartIdLoginPollQueryVariables = Types.Exact<{
  sessionId: Types.Scalars['String']['input'];
}>;

export type SmartIdLoginPollQuery = {
  __typename?: 'Query';
  challenge?: {
    __typename?: 'LoginChallenge';
    challenge_id?: string | null;
    session_id?: string | null;
    is_authenticated?: boolean | null;
  } | null;
};

export const SmartIdLoginChallengeDocument = gql`
  mutation SmartIdLoginChallenge($pin: String!) {
    challenge: smart_id_login_challenge(pin: $pin) {
      challenge_id
      session_id
      is_authenticated
    }
  }
`;
export type SmartIdLoginChallengeMutationFn = Apollo.MutationFunction<
  SmartIdLoginChallengeMutation,
  SmartIdLoginChallengeMutationVariables
>;

/**
 * __useSmartIdLoginChallengeMutation__
 *
 * To run a mutation, you first call `useSmartIdLoginChallengeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSmartIdLoginChallengeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [smartIdLoginChallengeMutation, { data, loading, error }] = useSmartIdLoginChallengeMutation({
 *   variables: {
 *      pin: // value for 'pin'
 *   },
 * });
 */
export function useSmartIdLoginChallengeMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SmartIdLoginChallengeMutation,
    SmartIdLoginChallengeMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SmartIdLoginChallengeMutation,
    SmartIdLoginChallengeMutationVariables
  >(SmartIdLoginChallengeDocument, options);
}
export type SmartIdLoginChallengeMutationHookResult = ReturnType<
  typeof useSmartIdLoginChallengeMutation
>;
export type SmartIdLoginChallengeMutationResult =
  Apollo.MutationResult<SmartIdLoginChallengeMutation>;
export type SmartIdLoginChallengeMutationOptions = Apollo.BaseMutationOptions<
  SmartIdLoginChallengeMutation,
  SmartIdLoginChallengeMutationVariables
>;
export const SmartIdLoginPollDocument = gql`
  query SmartIdLoginPoll($sessionId: String!) {
    challenge: smart_id_login(session_id: $sessionId) {
      challenge_id
      session_id
      is_authenticated
    }
  }
`;

/**
 * __useSmartIdLoginPollQuery__
 *
 * To run a query within a React component, call `useSmartIdLoginPollQuery` and pass it any options that fit your needs.
 * When your component renders, `useSmartIdLoginPollQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useSmartIdLoginPollQuery({
 *   variables: {
 *      sessionId: // value for 'sessionId'
 *   },
 * });
 */
export function useSmartIdLoginPollQuery(
  baseOptions: Apollo.QueryHookOptions<
    SmartIdLoginPollQuery,
    SmartIdLoginPollQueryVariables
  > &
    (
      | { variables: SmartIdLoginPollQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<SmartIdLoginPollQuery, SmartIdLoginPollQueryVariables>(
    SmartIdLoginPollDocument,
    options,
  );
}
export function useSmartIdLoginPollLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    SmartIdLoginPollQuery,
    SmartIdLoginPollQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    SmartIdLoginPollQuery,
    SmartIdLoginPollQueryVariables
  >(SmartIdLoginPollDocument, options);
}
export function useSmartIdLoginPollSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    SmartIdLoginPollQuery,
    SmartIdLoginPollQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    SmartIdLoginPollQuery,
    SmartIdLoginPollQueryVariables
  >(SmartIdLoginPollDocument, options);
}
export type SmartIdLoginPollQueryHookResult = ReturnType<
  typeof useSmartIdLoginPollQuery
>;
export type SmartIdLoginPollLazyQueryHookResult = ReturnType<
  typeof useSmartIdLoginPollLazyQuery
>;
export type SmartIdLoginPollSuspenseQueryHookResult = ReturnType<
  typeof useSmartIdLoginPollSuspenseQuery
>;
export type SmartIdLoginPollQueryResult = Apollo.QueryResult<
  SmartIdLoginPollQuery,
  SmartIdLoginPollQueryVariables
>;
