/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import type * as Types from '../../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
import { CustomerFragmentDoc } from '../../../../shared/api/user/queries.gen';
const defaultOptions = {} as const;
export type UpdateUserMutationVariables = Types.Exact<{
  userId: Types.Scalars['Int']['input'];
  firstName?: Types.InputMaybe<Types.Scalars['String']['input']>;
  lastName?: Types.InputMaybe<Types.Scalars['String']['input']>;
  email?: Types.InputMaybe<Types.Scalars['String']['input']>;
  phone?: Types.InputMaybe<Types.Scalars['String']['input']>;
  phoneAreaCode?: Types.InputMaybe<Types.Scalars['String']['input']>;
  languageAbbr?: Types.InputMaybe<Types.Scalars['String']['input']>;
  city?: Types.InputMaybe<Types.Scalars['String']['input']>;
  address?: Types.InputMaybe<Types.Scalars['String']['input']>;
  postCode?: Types.InputMaybe<Types.Scalars['String']['input']>;
  iban?: Types.InputMaybe<Types.Scalars['String']['input']>;
  net_income_monthly?: Types.InputMaybe<Types.Scalars['Float']['input']>;
  expenditure_monthly?: Types.InputMaybe<Types.Scalars['Float']['input']>;
  expenditure_consumer_loan_monthly?: Types.InputMaybe<
    Types.Scalars['Float']['input']
  >;
  employer?: Types.InputMaybe<Types.Scalars['String']['input']>;
  employment_sector?: Types.InputMaybe<Types.EmploymentSector>;
  occupation_category?: Types.InputMaybe<Types.OccupationCategory>;
  newsletter_agreement?: Types.InputMaybe<Types.Scalars['Boolean']['input']>;
  conditions_agreement?: Types.InputMaybe<Types.Scalars['Boolean']['input']>;
  allow_pension_query?: Types.InputMaybe<Types.Scalars['Boolean']['input']>;
  political_exposure?: Types.InputMaybe<Types.PoliticalExposure>;
  monthly_living_expenses?: Types.InputMaybe<Types.Scalars['Float']['input']>;
  number_of_dependents?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  ultimate_beneficial_owner?: Types.InputMaybe<
    Types.Scalars['Boolean']['input']
  >;
}>;

export type UpdateUserMutation = {
  __typename?: 'Mutation';
  user?: {
    __typename: 'User';
    id: number;
    email?: string | null;
    pin?: string | null;
    is_password_set: boolean;
    language_abbr: string;
    permission_bits: number;
    profile?: {
      __typename?: 'UserProfile';
      first_name?: string | null;
      last_name?: string | null;
    } | null;
    merchants?: Array<{
      __typename?: 'UserMerchant';
      id: number;
      name: string;
      merchant_permission_bits: number;
      send_emails: number;
      logo_path?: string | null;
      settings?: {
        __typename?: 'MerchantSettings';
        merchant_financing_pct: number;
      } | null;
      stores?: Array<{
        __typename?: 'MerchantStore';
        id: number;
        name: string;
      } | null> | null;
    } | null> | null;
  } | null;
};

export const UpdateUserDocument = gql`
  mutation UpdateUser(
    $userId: Int!
    $firstName: String
    $lastName: String
    $email: String
    $phone: String
    $phoneAreaCode: String
    $languageAbbr: String
    $city: String
    $address: String
    $postCode: String
    $iban: String
    $net_income_monthly: Float
    $expenditure_monthly: Float
    $expenditure_consumer_loan_monthly: Float
    $employer: String
    $employment_sector: EmploymentSector
    $occupation_category: OccupationCategory
    $newsletter_agreement: Boolean
    $conditions_agreement: Boolean
    $allow_pension_query: Boolean
    $political_exposure: PoliticalExposure
    $monthly_living_expenses: Float
    $number_of_dependents: Int
    $ultimate_beneficial_owner: Boolean
  ) {
    user: merchant_update_user(
      user_id: $userId
      first_name: $firstName
      last_name: $lastName
      email: $email
      phone: $phone
      phone_area_code: $phoneAreaCode
      language_abbr: $languageAbbr
      city: $city
      address: $address
      post_code: $postCode
      iban: $iban
      net_income_monthly: $net_income_monthly
      expenditure_monthly: $expenditure_monthly
      expenditure_consumer_loan_monthly: $expenditure_consumer_loan_monthly
      employer: $employer
      employment_sector: $employment_sector
      occupation_category: $occupation_category
      newsletter_agreement: $newsletter_agreement
      conditions_agreement: $conditions_agreement
      allow_pension_query: $allow_pension_query
      political_exposure: $political_exposure
      monthly_living_expenses: $monthly_living_expenses
      number_of_dependents: $number_of_dependents
      ultimate_beneficial_owner: $ultimate_beneficial_owner
    ) {
      ...Customer
    }
  }
  ${CustomerFragmentDoc}
`;
export type UpdateUserMutationFn = Apollo.MutationFunction<
  UpdateUserMutation,
  UpdateUserMutationVariables
>;

/**
 * __useUpdateUserMutation__
 *
 * To run a mutation, you first call `useUpdateUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateUserMutation, { data, loading, error }] = useUpdateUserMutation({
 *   variables: {
 *      userId: // value for 'userId'
 *      firstName: // value for 'firstName'
 *      lastName: // value for 'lastName'
 *      email: // value for 'email'
 *      phone: // value for 'phone'
 *      phoneAreaCode: // value for 'phoneAreaCode'
 *      languageAbbr: // value for 'languageAbbr'
 *      city: // value for 'city'
 *      address: // value for 'address'
 *      postCode: // value for 'postCode'
 *      iban: // value for 'iban'
 *      net_income_monthly: // value for 'net_income_monthly'
 *      expenditure_monthly: // value for 'expenditure_monthly'
 *      expenditure_consumer_loan_monthly: // value for 'expenditure_consumer_loan_monthly'
 *      employer: // value for 'employer'
 *      employment_sector: // value for 'employment_sector'
 *      occupation_category: // value for 'occupation_category'
 *      newsletter_agreement: // value for 'newsletter_agreement'
 *      conditions_agreement: // value for 'conditions_agreement'
 *      allow_pension_query: // value for 'allow_pension_query'
 *      political_exposure: // value for 'political_exposure'
 *      monthly_living_expenses: // value for 'monthly_living_expenses'
 *      number_of_dependents: // value for 'number_of_dependents'
 *      ultimate_beneficial_owner: // value for 'ultimate_beneficial_owner'
 *   },
 * });
 */
export function useUpdateUserMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateUserMutation,
    UpdateUserMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<UpdateUserMutation, UpdateUserMutationVariables>(
    UpdateUserDocument,
    options,
  );
}
export type UpdateUserMutationHookResult = ReturnType<
  typeof useUpdateUserMutation
>;
export type UpdateUserMutationResult =
  Apollo.MutationResult<UpdateUserMutation>;
export type UpdateUserMutationOptions = Apollo.BaseMutationOptions<
  UpdateUserMutation,
  UpdateUserMutationVariables
>;
