/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import type * as Types from '../../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type TransactionsQueryVariables = Types.Exact<{
  merchantId: Types.Scalars['Int']['input'];
  dateRange?: Types.InputMaybe<Types.DateRange>;
  methods?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.TransactionMerchantMethod>>
    | Types.InputMaybe<Types.TransactionMerchantMethod>
  >;
  bookTypes?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.TransactionBookType>>
    | Types.InputMaybe<Types.TransactionBookType>
  >;
  limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  page?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  merchantRefOrCustomerName?: Types.InputMaybe<
    Types.Scalars['String']['input']
  >;
}>;

export type TransactionsQuery = {
  __typename?: 'Query';
  transactions?: {
    __typename?: 'transaction_rowPagination';
    total: number;
    data?: Array<{
      __typename?: 'TransactionRow';
      id: number;
      from_amount: number;
      to_amount: number;
      transaction?: {
        __typename?: 'Transaction';
        transaction_date: string;
        method: string;
        payment?: {
          __typename?: 'Payment';
          description?: string | null;
          seb_notification?: {
            __typename?: 'SEBNotification';
            explanation?: string | null;
          } | null;
        } | null;
      } | null;
      book?: {
        __typename?: 'TransactionBook';
        type: Types.TransactionBookType;
      } | null;
      application_trashed?: {
        __typename?: 'Application';
        id: number;
        invoice_reference_nr?: string | null;
        for_private_person: boolean;
        status: Types.ApplicationStatus;
        deleted_at?: number | null;
        merchant_data?: {
          __typename?: 'ApplicationMerchantData';
          reference: string;
        } | null;
        user_info?: {
          __typename?: 'ApplicationUserInfo';
          first_name?: string | null;
          last_name?: string | null;
        } | null;
        legal_person_info?: {
          __typename?: 'ApplicationLegalPersonInfo';
          name?: string | null;
        } | null;
      } | null;
    } | null> | null;
  } | null;
};

export const TransactionsDocument = gql`
  query Transactions(
    $merchantId: Int!
    $dateRange: DateRange
    $methods: [TransactionMerchantMethod]
    $bookTypes: [TransactionBookType]
    $limit: Int
    $page: Int
    $merchantRefOrCustomerName: String
  ) {
    transactions: merchant_transactions(
      merchant_id: $merchantId
      date_range: $dateRange
      methods: $methods
      book_types: $bookTypes
      limit: $limit
      page: $page
      merchant_ref_or_customer_name: $merchantRefOrCustomerName
    ) {
      data {
        id
        transaction {
          transaction_date
          method
          payment {
            description
            seb_notification {
              explanation
            }
          }
        }
        book {
          type
        }
        from_amount
        to_amount
        application_trashed {
          id
          invoice_reference_nr
          for_private_person
          status
          merchant_data {
            reference
          }
          user_info {
            first_name
            last_name
          }
          legal_person_info {
            name
          }
          deleted_at
        }
      }
      total
    }
  }
`;

/**
 * __useTransactionsQuery__
 *
 * To run a query within a React component, call `useTransactionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useTransactionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useTransactionsQuery({
 *   variables: {
 *      merchantId: // value for 'merchantId'
 *      dateRange: // value for 'dateRange'
 *      methods: // value for 'methods'
 *      bookTypes: // value for 'bookTypes'
 *      limit: // value for 'limit'
 *      page: // value for 'page'
 *      merchantRefOrCustomerName: // value for 'merchantRefOrCustomerName'
 *   },
 * });
 */
export function useTransactionsQuery(
  baseOptions: Apollo.QueryHookOptions<
    TransactionsQuery,
    TransactionsQueryVariables
  > &
    (
      | { variables: TransactionsQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<TransactionsQuery, TransactionsQueryVariables>(
    TransactionsDocument,
    options,
  );
}
export function useTransactionsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    TransactionsQuery,
    TransactionsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<TransactionsQuery, TransactionsQueryVariables>(
    TransactionsDocument,
    options,
  );
}
export function useTransactionsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    TransactionsQuery,
    TransactionsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<TransactionsQuery, TransactionsQueryVariables>(
    TransactionsDocument,
    options,
  );
}
export type TransactionsQueryHookResult = ReturnType<
  typeof useTransactionsQuery
>;
export type TransactionsLazyQueryHookResult = ReturnType<
  typeof useTransactionsLazyQuery
>;
export type TransactionsSuspenseQueryHookResult = ReturnType<
  typeof useTransactionsSuspenseQuery
>;
export type TransactionsQueryResult = Apollo.QueryResult<
  TransactionsQuery,
  TransactionsQueryVariables
>;
