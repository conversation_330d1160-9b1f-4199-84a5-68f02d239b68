import type { AvailableLanguage } from 'shared/types';
import { AppRegions } from 'shared/utils';

const BASE_URLS = {
  ET: 'https://esto-public.s3.eu-west-2.amazonaws.com/docs',
  LV: 'https://esto-lv-public.s3.eu-central-1.amazonaws.com/docs',
  LT: 'https://esto-lt-public.s3.eu-central-1.amazonaws.com/docs',
} as const;

const PRIVACY_POLICY_DOCUMENTS = {
  ET: {
    en: 'en-us/Rules%20of%20processing%20personal%20data.pdf',
    et: 'et-ee/Isikuandmete%20t%C3%B6%C3%B6tlemise%20reeglid.pdf',
    ru: 'ru-ru/%D0%9F%D1%80%D0%B0%D0%B2%D0%B8%D0%BB%D0%B0%20%D0%BE%D0%B1%D1%80%D0%B0%D0%B1%D0%BE%D1%82%D0%BA%D0%B8%20%D0%BB%D0%B8%D1%87%D0%BD%D1%8B%D1%85%20%D0%B4%D0%B0%D0%BD%D0%BD%D1%8B%D1%85.pdf',
  },
  LV: {
    en: 'lv-lv/Priva%CC%84tuma%20politika.pdf',
    lv: 'lv-lv/Priva%CC%84tuma%20politika.pdf',
    ru: 'lv-lv/Priva%CC%84tuma%20politika.pdf',
  },
  LT: {
    en: 'lt-lt/ESTO%20Privatumo%20politika.pdf',
    lt: 'lt-lt/ESTO%20Privatumo%20politika.pdf',
    ru: 'ru-ru/%D0%9F%D1%80%D0%B0%D0%B2%D0%B8%D0%BB%D0%B0%20%D0%BE%D0%B1%D1%80%D0%B0%D0%B1%D0%BE%D1%82%D0%BA%D0%B8%20%D0%BB%D0%B8%D1%87%D0%BD%D1%8B%D1%85%20%D0%B4%D0%B0%D0%BD%D0%BD%D1%8B%D1%85.pdf',
  },
} as const;

const createUrl = (region: keyof typeof BASE_URLS, document: string): string =>
  `${BASE_URLS[region]}/${document}`;

export const PRIVACY_POLICY_URLS = {
  [AppRegions.ET]: {
    en: createUrl('ET', PRIVACY_POLICY_DOCUMENTS.ET.en),
    et: createUrl('ET', PRIVACY_POLICY_DOCUMENTS.ET.et),
    ru: createUrl('ET', PRIVACY_POLICY_DOCUMENTS.ET.ru),
  },
  [AppRegions.LV]: {
    en: createUrl('LV', PRIVACY_POLICY_DOCUMENTS.LV.en),
    lv: createUrl('LV', PRIVACY_POLICY_DOCUMENTS.LV.lv),
    ru: createUrl('LV', PRIVACY_POLICY_DOCUMENTS.LV.ru),
  },
  [AppRegions.LT]: {
    en: createUrl('LT', PRIVACY_POLICY_DOCUMENTS.LT.en),
    lt: createUrl('LT', PRIVACY_POLICY_DOCUMENTS.LT.lt),
    ru: createUrl('LT', PRIVACY_POLICY_DOCUMENTS.LT.ru),
  },
} as const;

export const getPrivacyPolicyUrl = (
  region: AppRegions,
  language: AvailableLanguage,
): string => {
  const regionUrls = PRIVACY_POLICY_URLS[region];

  if (!regionUrls) {
    return PRIVACY_POLICY_URLS[AppRegions.ET].en;
  }

  const url = regionUrls[language as keyof typeof regionUrls];

  if (!url) {
    const firstAvailableUrl = Object.values(regionUrls)[0];
    return firstAvailableUrl;
  }

  return url;
};
