/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import type * as Types from '../../../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ScheduleTypeSettingsQueryVariables = Types.Exact<{
  scheduleType: Types.ApplicationScheduleType;
}>;

export type ScheduleTypeSettingsQuery = {
  __typename?: 'Query';
  schedule_type_settings?: {
    __typename?: 'ScheduleTypeSettings';
    min_loan_amount: number;
    max_loan_amount: number;
  } | null;
};

export const ScheduleTypeSettingsDocument = gql`
  query ScheduleTypeSettings($scheduleType: ApplicationScheduleType!) {
    schedule_type_settings(schedule_type: $scheduleType) {
      min_loan_amount
      max_loan_amount
    }
  }
`;

/**
 * __useScheduleTypeSettingsQuery__
 *
 * To run a query within a React component, call `useScheduleTypeSettingsQuery` and pass it any options that fit your needs.
 * When your component renders, `useScheduleTypeSettingsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useScheduleTypeSettingsQuery({
 *   variables: {
 *      scheduleType: // value for 'scheduleType'
 *   },
 * });
 */
export function useScheduleTypeSettingsQuery(
  baseOptions: Apollo.QueryHookOptions<
    ScheduleTypeSettingsQuery,
    ScheduleTypeSettingsQueryVariables
  > &
    (
      | { variables: ScheduleTypeSettingsQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    ScheduleTypeSettingsQuery,
    ScheduleTypeSettingsQueryVariables
  >(ScheduleTypeSettingsDocument, options);
}
export function useScheduleTypeSettingsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    ScheduleTypeSettingsQuery,
    ScheduleTypeSettingsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    ScheduleTypeSettingsQuery,
    ScheduleTypeSettingsQueryVariables
  >(ScheduleTypeSettingsDocument, options);
}
export function useScheduleTypeSettingsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    ScheduleTypeSettingsQuery,
    ScheduleTypeSettingsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    ScheduleTypeSettingsQuery,
    ScheduleTypeSettingsQueryVariables
  >(ScheduleTypeSettingsDocument, options);
}
export type ScheduleTypeSettingsQueryHookResult = ReturnType<
  typeof useScheduleTypeSettingsQuery
>;
export type ScheduleTypeSettingsLazyQueryHookResult = ReturnType<
  typeof useScheduleTypeSettingsLazyQuery
>;
export type ScheduleTypeSettingsSuspenseQueryHookResult = ReturnType<
  typeof useScheduleTypeSettingsSuspenseQuery
>;
export type ScheduleTypeSettingsQueryResult = Apollo.QueryResult<
  ScheduleTypeSettingsQuery,
  ScheduleTypeSettingsQueryVariables
>;
