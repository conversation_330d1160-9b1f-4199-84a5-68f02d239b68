import { Flex, Icon, Text, Tooltip } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { LocizeNamespaces } from 'shared/constants/localization-keys';
import { isEligibilityStatusTooltipEnabled } from 'shared/lib';

import { getEligibilityStatusInfo } from './EligibilityStatusInfo.utils';

/**
 * Represents a Props object.
 * @typedef {Object} Props
 * @property {string} simpleEligibilityStatus - The simple eligibility status.
 * @property {number} eligibilityState - The eligibility state.
 */
type Props = {
  simpleEligibilityStatus: string;
  eligibilityState?: number | null;
};

/**
 * Represents the eligibility status information component.
 *
 * @typedef {Object} EligibilityStatusInfo
 * @property {string} simpleEligibilityStatus - The simple eligibility status.
 * @property {string} eligibilityState - The eligibility state.
 */
export const EligibilityStatusInfo = ({
  simpleEligibilityStatus,
  eligibilityState,
}: Props) => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);

  const eligibilityStatusData = getEligibilityStatusInfo({
    simpleEligibilityStatus,
    eligibilityState,
  });

  if (!eligibilityStatusData) {
    return null;
  }

  const { icon, uiLabel, tooltipText, color } = eligibilityStatusData;

  return (
    <Tooltip
      arrowSize={8}
      hasArrow
      isDisabled={
        !isEligibilityStatusTooltipEnabled ||
        (isEligibilityStatusTooltipEnabled && !tooltipText)
      }
      label={tooltipText}
      placement="top"
    >
      <Flex alignItems="center" display="inline-flex" gap={1} w="auto">
        <Text>{t(`eligibility-statuses.${uiLabel}`)}</Text>
        <Icon as={icon} boxSize={5} color={color} ml={1} />
      </Flex>
    </Tooltip>
  );
};
